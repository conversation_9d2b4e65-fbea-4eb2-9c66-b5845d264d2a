package org.jeecg.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Author: ggr
 * @CreateTime: 2025-08-26
 * @Description:
 * @Version: 1.0
 */

@Data
public class FbmCustomerVo {
    @Excel(name = "id", width = 15)
    @ApiModelProperty(value = "id")
    private String id;
    /**联系人姓名*/
    @Excel(name = "客户联系人", width = 15)
    @ApiModelProperty(value = "客户联系人")
    private String concatName;
    /**客户名字*/
    @Excel(name = "客户名称", width = 15)
    @ApiModelProperty(value = "客户名称")
    private String customerName;
    /**明抄送邮箱*/
    @Excel(name = "明抄送邮箱", width = 15)
    @ApiModelProperty(value = "明抄送邮箱")
    private String ccmail;
    /**法定代表人姓名*/
    @Excel(name = "客户法定代表人", width = 15)
    @ApiModelProperty(value = "客户法定代表人")
    private String legalRepresentative;
    /**营业执照编号*/
    @Excel(name = "营业执照编号", width = 15)
    @ApiModelProperty(value = "营业执照编号")
    private String businessLicense;

    @Excel(name="检测作品数" ,width = 15)
    @ApiModelProperty(value = "监测作品数")
    private String numberDetectedWorks;

    @Excel(name="侵权数" ,width = 15)
    @ApiModelProperty(value = "侵权数")
    private String numberTortData;

    @Excel(name="站点数" ,width = 15)
    @ApiModelProperty(value = "站点数")
    private String numberSite;
}
