package org.jeecg.entity.param;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Author: ggr
 * @CreateTime: 2025-08-26
 * @Description:
 * @Version: 1.0
 */

@Data
public class FbmCustomerAddParam {
    /**联系人姓名*/
    @Excel(name = "客户联系人", width = 15)
    @ApiModelProperty(value = "客户联系人")
    private java.lang.String concatName;
    /**客户名字*/
    @Excel(name = "客户名称", width = 15)
    @ApiModelProperty(value = "客户名称")
    private java.lang.String customerName;
    /**客户英文名称*/
    @Excel(name = "客户英文名称", width = 15)
    @ApiModelProperty(value = "客户英文名称")
    private java.lang.String englishName;
    /**明抄送邮箱*/
    @Excel(name = "明抄送邮箱", width = 15)
    @ApiModelProperty(value = "明抄送邮箱")
    private java.lang.String ccmail;
    /**客户全部名字*/
    @Excel(name = "客户全称", width = 15)
    @ApiModelProperty(value = "客户全称")
    private java.lang.String customerFullName;
    /**法定代表人姓名*/
    @Excel(name = "客户法定代表人", width = 15)
    @ApiModelProperty(value = "客户法定代表人")
    private java.lang.String legalRepresentative;
    /**营业执照编号*/
    @Excel(name = "营业执照编号", width = 15)
    @ApiModelProperty(value = "营业执照编号")
    private java.lang.String businessLicense;
    /**组织机构代码号、社会信用代码号或工商注册号*/
    @Excel(name = "统一社会信用代码", width = 15)
    @ApiModelProperty(value = "统一社会信用代码")
    private java.lang.String orgCode;
}
