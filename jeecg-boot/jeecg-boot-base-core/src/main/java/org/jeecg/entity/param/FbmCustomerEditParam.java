package org.jeecg.entity.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Author: ggr
 * @CreateTime: 2025-08-26
 * @Description:
 * @Version: 1.0
 */

@Data
public class FbmCustomerEditParam {
    @Excel(name = "id", width = 15)
    @ApiModelProperty(value = "id")
    private String id;
    /**联系人姓名*/
    @Excel(name = "客户联系人", width = 15)
    @ApiModelProperty(value = "客户联系人")
    private String concatName;
    /**客户名字*/
    @Excel(name = "客户名称", width = 15)
    @ApiModelProperty(value = "客户名称")
    private String customerName;
    /**客户英文名称*/
    @Excel(name = "客户英文名称", width = 15)
    @ApiModelProperty(value = "客户英文名称")
    private String englishName;
    /**明抄送邮箱*/
    @Excel(name = "明抄送邮箱", width = 15)
    @ApiModelProperty(value = "明抄送邮箱")
    private String ccmail;
    /**客户全部名字*/
    @Excel(name = "客户全称", width = 15)
    @ApiModelProperty(value = "客户全称")
    private String customerFullName;
    /**法定代表人姓名*/
    @Excel(name = "客户法定代表人", width = 15)
    @ApiModelProperty(value = "客户法定代表人")
    private String legalRepresentative;
    /**营业执照编号*/
    @Excel(name = "营业执照编号", width = 15)
    @ApiModelProperty(value = "营业执照编号")
    private String businessLicense;
    /**组织机构代码号、社会信用代码号或工商注册号*/
    @Excel(name = "统一社会信用代码", width = 15)
    @ApiModelProperty(value = "统一社会信用代码")
    private String orgCode;
}
