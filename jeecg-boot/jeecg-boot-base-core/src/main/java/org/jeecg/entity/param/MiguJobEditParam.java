package org.jeecg.entity.param;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.List;

/**
 * @Author: ggr
 * @CreateTime: 2025-08-26
 * @Description: 任务管理编辑参数
 * @Version: 1.0
 */

@Data
public class MiguJobEditParam {
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private java.lang.Integer id;
    /**
     * 任务名
     */
    @Excel(name = "任务名", width = 15)
    @ApiModelProperty(value = "任务名")
    private java.lang.String jobName;
    /**
     * 监测内容
     */
    @Excel(name = "监测内容", width = 15)
    @ApiModelProperty(value = "监测内容")
    private java.lang.String content;
    /**
     * 版权信息
     */
    @Excel(name = "版权信息", width = 15)
    @ApiModelProperty(value = "权力信息")
    private java.lang.String copyrightInfo;
    /**
     * 监测范围  0直播，1点播
     */
    @Excel(name = "监测范围  0直播，1点播", width = 15)
    @ApiModelProperty(value = "监测范围  0直播，1点播")
    private java.lang.String scope;
    /**
     * 权力文件地址
     */
    @Excel(name = "权力文件地址", width = 15)
    @ApiModelProperty(value = "权力文件地址")
    private List<String> attachmentPath;
    /**
     * 直播时间
     */
    @Excel(name = "直播时间", width = 15)
    @ApiModelProperty(value = "直播时间")
    private List<java.lang.String> liveTimeList;
    /**
     * 点播监测条件
     */
    @Excel(name = "点播监测条件", width = 15)
    @ApiModelProperty(value = "点播监测条件")
    private List<java.lang.String> demandList;
    /**
     * 重点监测平台
     */
    @Excel(name = "重点监测平台", width = 15)
    @ApiModelProperty(value = "重点监测平台")
    private List<java.lang.String> siteList;
    /**
     * 重点关注平台
     */
    @Excel(name = "白名单信息", width = 15)
    @ApiModelProperty(value = "重点监测平台")
    private List<java.lang.String> whiteInfoList;
    /**
     * 0 日报，1 周报 ，2 月报
     */
    @Excel(name = "0 日报，1 周报 ，2 月报", width = 15)
    @ApiModelProperty(value = "0 日报，1 周报 ，2 月报")
    private java.lang.Integer liveReport;
    /**
     * 0 日报，1 周报 ，2 月报
     */
    @Excel(name = "0 日报，1 周报 ，2 月报", width = 15)
    @ApiModelProperty(value = "0 日报，1 周报 ，2 月报")
    private java.lang.Integer demandReport;
    /**
     * 描述
     */
    @Excel(name = "描述", width = 15)
    @ApiModelProperty(value = "描述")
    private java.lang.String description;
}
