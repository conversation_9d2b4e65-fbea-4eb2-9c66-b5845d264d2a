package org.jeecg.modules.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.entity.FbmCustomerCount;
import org.jeecg.entity.FbmTortDataVideo;
import org.jeecg.entity.vo.account.AccountWorkDetailVo;

import java.util.List;
import java.util.Map;

/**
 * @Description: 视频侵权表
 * @Author: jeecg-boot
 * @Date: 2024-09-05
 * @Version: V1.0
 */
public interface IFbmTortDataVideoService extends IService<FbmTortDataVideo> {

	List<FbmTortDataVideo> listByPublisher(long lastId, String siteShowName, String publisherId, String publisher);

	AccountWorkDetailVo.TortClueInfo selectTortClueInfo(Long worksId);
	Map<String, Long> getCountNum(List<Long> worksIds);
}
