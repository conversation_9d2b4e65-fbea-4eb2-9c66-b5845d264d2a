package org.jeecg.modules.platform.service.impl;

import org.jeecg.entity.FbmCustomerCount;
import org.jeecg.modules.platform.mapper.FbmCustomerCountMapper;
import org.jeecg.modules.platform.service.IFbmCustomerCountService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 客户管理页面数据统计
 * @Author: jeecg-boot
 * @Date:   2025-08-27
 * @Version: V1.0
 */
@Service
public class FbmCustomerCountServiceImpl extends ServiceImpl<FbmCustomerCountMapper, FbmCustomerCount> implements IFbmCustomerCountService {

    @Override
    public FbmCustomerCount getCountTortAndSite() {
        return this.baseMapper.getCountTortAndSite();
    }
}
