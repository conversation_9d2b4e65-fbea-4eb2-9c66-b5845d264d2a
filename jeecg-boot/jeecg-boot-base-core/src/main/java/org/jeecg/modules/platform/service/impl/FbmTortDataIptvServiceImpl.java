package org.jeecg.modules.platform.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.ibatis.annotations.Select;
import org.jeecg.entity.FbmCustomerCount;
import org.jeecg.entity.FbmTortDataIptv;
import org.jeecg.entity.bo.MiguWorksSiteMailOfflineBo;
import org.jeecg.entity.bo.MiguWorksSiteMailOnlineBo;
import org.jeecg.entity.param.MiguWorksSiteOfflinePrParam;
import org.jeecg.modules.platform.mapper.FbmTortDataIptvMapper;
import org.jeecg.modules.platform.service.IFbmTortDataIptvService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @Description: iptv侵权表
 * @Author: jeecg-boot
 * @Date:   2024-09-05
 * @Version: V1.0
 */
@Service
public class FbmTortDataIptvServiceImpl extends ServiceImpl<FbmTortDataIptvMapper, FbmTortDataIptv> implements IFbmTortDataIptvService {


    public List<Long> selectWorksIds() {
        return this.baseMapper.selectWorksIds();
    }
    public Map<String, Long> getCountNum(List<Long> worksIds) {
        return this.baseMapper.getCountNum(worksIds);
    }

}
