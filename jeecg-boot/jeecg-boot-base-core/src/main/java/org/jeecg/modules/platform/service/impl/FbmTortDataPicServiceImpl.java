package org.jeecg.modules.platform.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.entity.FbmCustomerCount;
import org.jeecg.entity.FbmTortDataPic;
import org.jeecg.entity.bo.MiguWorksSiteMailOfflineBo;
import org.jeecg.entity.bo.MiguWorksSiteMailOnlineBo;
import org.jeecg.entity.bo.SiteTopBo;
import org.jeecg.entity.param.MiguWorksSiteOfflinePrParam;
import org.jeecg.modules.platform.mapper.FbmTortDataPicMapper;
import org.jeecg.modules.platform.service.IFbmTortDataPicService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @Description: 图片侵权表
 * @Author: jeecg-boot
 * @Date:   2024-09-05
 * @Version: V1.0
 */
@Service
public class FbmTortDataPicServiceImpl extends ServiceImpl<FbmTortDataPicMapper, FbmTortDataPic> implements IFbmTortDataPicService {

    public List<SiteTopBo> groupBySiteShowName(List<Long> worksIds) {
        return this.baseMapper.groupBySiteShowName(worksIds);
    }

    public List<MiguWorksSiteMailOfflineBo> getSiteMailOfflineCount(MiguWorksSiteOfflinePrParam param) {
        return this.baseMapper.getSiteMailOfflineCount(param);
    }

    public List<MiguWorksSiteMailOnlineBo> getSiteMailOnlineCount(MiguWorksSiteOfflinePrParam param) {
        return this.baseMapper.getSiteMailOnlineCount(param);
    }

    public Map<String, Long> getCountNum(List<Long> partition) {
        return this.baseMapper.getCountNum(partition);
    }
}
