package org.jeecg.modules.platform.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;
import org.jeecg.entity.FbmJob;

/**
 * @Description: 任务表
 * @Author: jeecg-boot
 * @Date:   2024-09-04
 * @Version: V1.0
 */
@DS(value = "db-38")
public interface FbmJobMapper extends BaseMapper<FbmJob> {

	@Select("select fj.* " +
			"from fbm_job fj " +
			"inner join fbm_job_works_mapping fjwm on fjwm.job_id = fj.id " +
			"where fjwm.works_id = 1 " +
			"order by fjwm.id desc " +
			"limit 1")
	FbmJob selectByWorksId(Long worksId);
}
