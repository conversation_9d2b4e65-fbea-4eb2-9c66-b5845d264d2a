package org.jeecg.modules.platform.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;
import org.jeecg.entity.FbmCustomerCount;

/**
 * @Description: 客户管理页面数据统计
 * @Author: jeecg-boot
 * @Date:   2025-08-27
 * @Version: V1.0
 */
public interface FbmCustomerCountMapper extends BaseMapper<FbmCustomerCount> {

    @Select("SELECT COUNT(tort_count) as tortCount, COUNT(site_count) as siteCount FROM fbm_customer_count ")
    FbmCustomerCount getCountTortAndSite();
}
