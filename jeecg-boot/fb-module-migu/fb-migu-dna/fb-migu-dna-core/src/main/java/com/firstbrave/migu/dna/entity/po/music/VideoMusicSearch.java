package com.firstbrave.migu.dna.entity.po.music;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@TableName("video_music_search")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "video_music_search对象", description = "1")
public class VideoMusicSearch {

	/**
	 * id
	 */
	@TableId(type = IdType.AUTO)
	@ApiModelProperty(value = "id")
	private Long id;
	/**
	 * 视频唯一值，请求方
	 */
	@ApiModelProperty(value = "视频唯一值，请求方")
	private String videoId;
	/**
	 * 视频所属库
	 */
	@ApiModelProperty(value = "视频所属库")
	private String lib;
	/**
	 * 指纹唯一值(MD5(lib + videoId))
	 */
	@ApiModelProperty(value = "指纹唯一值(MD5(lib + videoId))")
	private String dnaMd5;
	/**
	 * 需要检索的库（多个,分割）
	 */
	@ApiModelProperty(value = "需要检索的库（多个,分割）")
	private String searchLibs;
	/**
	 * 优先级：0-5，5的优先级最低
	 */
	@ApiModelProperty(value = "优先级：0-5，5的优先级最低")
	private Integer priority;
	/**
	 * 处理状态：0: 未处理 1: 队列中 2：处理中 3：处理结束 4：回调成功 5：回调失败
	 */
	@ApiModelProperty(value = "处理状态：0: 未处理 1: 队列中 2：处理中 3：处理结束 4：回调成功 5：回调失败")
	private Integer handleStatus;
	/**
	 * video_music_task.id，如果为null，说明直接调用并回调
	 */
	@ApiModelProperty(value = "video_music_task.id，如果为null，说明直接调用并回调")
	private Long taskId;
	/**
	 * 回调路径
	 */
	@ApiModelProperty(value = "回调路径")
	private String callbackUrl;
	/**
	 * 回调token
	 */
	@ApiModelProperty(value = "回调token")
	private String callbackToken;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;
}
