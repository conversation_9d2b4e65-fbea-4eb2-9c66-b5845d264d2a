
package com.firstbrave.ypc;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.firstbrave.MiguApplication;
import com.firstbrave.job.PersonalInformationEncryptionJob;
import com.firstbrave.modules.system.entity.SysUser;
import com.firstbrave.modules.system.mapper.SysUserMapper;
import com.firstbrave.modules.system.service.impl.SysUserServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.entity.po.user.FbmUserInfo;
import org.jeecg.modules.platform.service.impl.FbmUserInfoServiceImpl;
import org.jeecg.utils.ISM4Utils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: ggr
 * @CreateTime: 2025-07-02
 * @Description: 用户信息加密迁移测试
 * @Version: 1.0
 */

@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest(classes = MiguApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class GgrTest {
    @Resource
    SysUserServiceImpl sysUserService;
    @Resource
    FbmUserInfoServiceImpl fbmUserInfoService;
    @Resource
    SysUserMapper sysUserMapper;

    @Resource
    PersonalInformationEncryptionJob personalInformationEncryptionJob;
    /**
     * 用户信息加密迁移测试
     * 注意：@Rollback(false) 表示不回滚事务，数据会真正保存到数据库
     * 如果只是测试而不想保存数据，请移除 @Rollback(false) 或设置为 @Rollback(true)
     */
    @Test
    @Rollback(false)
    @Transactional// 设置为 false 表示不回滚，数据会真正保存
    public void run() throws Exception {
        personalInformationEncryptionJob.run("");
    }

    /**
     * 处理单个用户信息的加密
     */

    public void processUserInfo(FbmUserInfo fbmUserInfo) throws Exception {
        // 添加空值检查
        if (StrUtil.isNotBlank(fbmUserInfo.getEmail())) {
            String emailEncrypt = ISM4Utils.encryptSm4Ecb(fbmUserInfo.getEmail());
            fbmUserInfo.setEmail(emailEncrypt);
        }

        if (StrUtil.isNotBlank(fbmUserInfo.getMobile())) {
            String mobileEncrypt = ISM4Utils.encryptSm4Ecb(fbmUserInfo.getMobile());
            fbmUserInfo.setMobile(mobileEncrypt);
        }

        if (StrUtil.isNotBlank(fbmUserInfo.getPhone())) {
            String phoneEncrypted = ISM4Utils.encryptSm4Ecb(fbmUserInfo.getPhone());
            fbmUserInfo.setPhone(phoneEncrypted);
        }

        fbmUserInfo.setVersion(4);
        fbmUserInfoService.updateById(fbmUserInfo);

        // 加密对应的 SysUser 信息，添加空值检查
        Long userId = fbmUserInfo.getUserId();
        if (userId != null) {
            // 将 Long 类型的 userId 转换为 String 类型，因为 SysUser 的主键是 String 类型
            String userIdStr = String.valueOf(userId);
            log.info("查询用户ID: " + userIdStr);
            SysUser sysUser = sysUserMapper.selectById(userIdStr);
            if (sysUser != null) {
                log.info("找到用户: " + sysUser.getUsername());
                if (StrUtil.isNotBlank(sysUser.getEmail())) {
                    sysUser.setEmail(ISM4Utils.encryptSm4Ecb(sysUser.getEmail()));
                }
                if (StrUtil.isNotBlank(sysUser.getPhone())) {
                    sysUser.setPhone(ISM4Utils.encryptSm4Ecb(sysUser.getPhone()));
                }
                if (StrUtil.isNotBlank(sysUser.getTelephone())) {
                    sysUser.setTelephone(ISM4Utils.encryptSm4Ecb(sysUser.getTelephone()));
                }

                sysUserService.updateById( sysUser);
            } else {
                log.warn("未找到用户ID为 {} 的用户", userIdStr);
            }
        }
    }
}
