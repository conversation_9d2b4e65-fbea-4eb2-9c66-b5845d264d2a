package com.firstbrave.ggr;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.firstbrave.MiguApplication;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.entity.*;
import org.jeecg.modules.platform.service.impl.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: ggr
 * @CreateTime: 2025-08-27
 * @Description:
 * @Version: 1.0
 */

@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest(classes = MiguApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CustomerTest {
    @Resource
    FbmCustomerWorksAttachmentServiceImpl fbmCustomerWorksAttachmentService;

    @Resource
    FbmTortDataVideoServiceImpl fbmTortDataVideoService;
    @Resource
    FbmWorksServiceImpl fbmWorksService;

    @Test
    public void getWorksByCustomer(Long customerId) {
        List<Object> worksIds = fbmCustomerWorksAttachmentService.listObjs(
                Wrappers.<FbmCustomerWorksAttachment>query()
                        .select("DISTINCT works_id")
                        .eq("customer_id", customerId)
                        .eq("status", "1")
        );
        int worksCount = worksIds.size();
    }
    @Resource
    FbmTortDataAudioServiceImpl fbmTortDataAudioService;
    @Resource
    FbmTortDataTxtServiceImpl fbmTortDataTxtService;
    @Resource
    FbmTortDataPicServiceImpl fbmTortDataPicService;
    @Resource
    FbmTortDataIptvServiceImpl fbmTortDataIptvService;
    @Resource
    FbmCustomerCountServiceImpl fbmCustomerCountService;

    @Test
    public void getTortNumberByCustomer(Long customerId) {
        //获取作品id
        List<Object> worksIds = fbmCustomerWorksAttachmentService.listObjs(
                Wrappers.<FbmCustomerWorksAttachment>query()
                        .select("DISTINCT works_id")
                        .eq("customer_id", customerId)
                        .eq("status", "1")
        );
        // 按照分类分组统计
        Map<String, List<Long>> categoryWorksMap = fbmWorksService.list(
                        Wrappers.<FbmWorks>lambdaQuery()
                                .in(FbmWorks::getId, worksIds)
                ).stream()
                .collect(Collectors.groupingBy(
                        FbmWorks::getFirstCategory,
                        Collectors.mapping(
                                FbmWorks::getId,  // 提取作品ID
                                Collectors.toList()  // 收集为列表
                        )
                ));
        //统计侵权数
        Long tortCount = 0L;
        for (String category : categoryWorksMap.keySet()) {
            List<Long> worksIdList = categoryWorksMap.get(category);
            switch (category)
                {
                    case "1":
                        tortCount+=fbmTortDataVideoService.count(Wrappers.<FbmTortDataVideo>lambdaQuery().in(
                            FbmTortDataVideo::getWorksId, worksIdList).eq(FbmTortDataVideo::getTortIsDel, 1));break;
                    case "2":
                        tortCount+=fbmTortDataAudioService.count(Wrappers.<FbmTortDataAudio>lambdaQuery().in(
                                FbmTortDataAudio::getWorksId, worksIdList).eq(FbmTortDataAudio::getTortIsDel, 1));break;
                    case "3":
                        tortCount+=fbmTortDataTxtService.count(Wrappers.<FbmTortDataTxt>lambdaQuery().in(
                                FbmTortDataTxt::getWorksId, worksIdList).eq(FbmTortDataTxt::getTortIsDel, 1));break;
                    case "4":
                        tortCount+=fbmTortDataPicService.count(Wrappers.<FbmTortDataPic>lambdaQuery().in(
                                FbmTortDataPic::getWorksId, worksIdList).eq(FbmTortDataPic::getTortIsDel, 1));break;
                    default:
                        tortCount+=fbmTortDataIptvService.count(Wrappers.<FbmTortDataIptv>lambdaQuery().in(
                                FbmTortDataIptv::getWorksId, worksIdList).eq(FbmTortDataIptv::getTortIsDel, 1));break;
                }
        }
        //保存
        fbmCustomerCountService.saveOrUpdate(
                FbmCustomerCount.builder()
                        .customerId(customerId)
                        .worksCount(String.valueOf(worksIds.size()) )
                        .tortCount(String.valueOf(tortCount))
                        .siteCount()
                        .build()
        );



    }


}
