package com.firstbrave.controller.customer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ultrapower.casp.client.LoginUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.util.JeecgDataAutorUtils;
import org.jeecg.common.system.vo.SysUserCacheInfo;
import org.jeecg.common.util.LongUtils;
import org.jeecg.common.util.PageUtil;
import org.jeecg.entity.FbmCustomer;
import org.jeecg.entity.FbmCustomerCount;
import org.jeecg.entity.param.FbmCustomerAddParam;
import org.jeecg.entity.param.FbmCustomerEditParam;
import org.jeecg.entity.param.FbmCustomerParam;
import org.jeecg.entity.vo.FbmCustomerVo;
import org.jeecg.modules.platform.service.IFbmCustomerCountService;
import org.jeecg.modules.platform.service.IFbmCustomerService;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * @Description: 客户管理
 * @Author: ggr
 * @Date: 2025-08-26
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "客户管理")
@RestController
@RequestMapping("/fbmCustomer")
public class FbmCustomerController extends JeecgController<FbmCustomer, IFbmCustomerService> {
    @Resource
    private IFbmCustomerService fbmCustomerService;
    @Resource
    private IFbmCustomerCountService fbmCustomerCountService;
    /**
     * 分页列表查询
     *
     * @param fbmCustomerParam
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "客户管理-分页列表查询")
    @ApiOperation(value = "客户管理-分页列表查询", notes = "客户管理-分页列表查询")
    @PostMapping(value = "/list")
    public Result<IPage<FbmCustomerVo>> queryPageList(@RequestBody FbmCustomerParam fbmCustomerParam,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        FbmCustomer fbmCustomer = BeanUtil.copyProperties(fbmCustomerParam, FbmCustomer.class);
        QueryWrapper<FbmCustomer> queryWrapper = QueryGenerator.initQueryWrapper(fbmCustomer, req.getParameterMap());
        Page<FbmCustomer> page = new Page<FbmCustomer>(pageNo, pageSize);
        IPage<FbmCustomer> pageList = fbmCustomerService.page(page, queryWrapper);
        Page<FbmCustomerVo> generate = PageUtil.generate(pageList, FbmCustomerVo.class);
        for (FbmCustomerVo record : generate.getRecords()) {
            FbmCustomerVo fbmCustomerVo = BeanUtil.copyProperties(record, FbmCustomerVo.class);
            FbmCustomerCount byId = fbmCustomerCountService.getById(record.getId());
            if (byId == null) {
                fbmCustomerVo.setNumberDetectedWorks("0");
                fbmCustomerVo.setNumberTortData("0");
                fbmCustomerVo.setNumberSite("0");
                continue;
            }
            fbmCustomerVo.setNumberSite(byId.getSiteCount());
            fbmCustomerVo.setNumberTortData(byId.getTortCount());
            fbmCustomerVo.setNumberDetectedWorks(byId.getWorksCount());
        }
        return Result.OK(generate);
    }

    /**
     * 添加
     *
     * @param fbmCustomerAddParam
     * @return
     */
    @AutoLog(value = "客户管理-添加")
    @ApiOperation(value = "客户管理-添加", notes = "客户管理-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody FbmCustomerAddParam fbmCustomerAddParam) {
        FbmCustomer fbmCustomer = BeanUtil.copyProperties(fbmCustomerAddParam, FbmCustomer.class);
        fbmCustomer.setVersion(1);
        fbmCustomer.setCaoncatTel("");
        fbmCustomer.setCity("");
        fbmCustomer.setCreateUserId(0);
        if (JeecgDataAutorUtils.loadUserInfo()!=null) {
            SysUserCacheInfo user = JeecgDataAutorUtils.loadUserInfo();
            String userId = user.getSysUserId();
            fbmCustomer.setCreateUserId(Integer.parseInt(userId));
        }
        fbmCustomer.setProvince("");
        fbmCustomer.setDescription("");


        fbmCustomerService.save(fbmCustomer);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param fbmCustomerEditParam
     * @return
     */
    @AutoLog(value = "客户管理-编辑")
    @ApiOperation(value = "客户管理-编辑", notes = "客户管理-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody FbmCustomerEditParam fbmCustomerEditParam) {
        FbmCustomer fbmCustomer = BeanUtil.copyProperties(fbmCustomerEditParam, FbmCustomer.class);
        fbmCustomerService.updateById(fbmCustomer);
        return Result.OK("编辑成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "客户管理-批量删除")
    @ApiOperation(value = "客户管理-批量删除", notes = "客户管理-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.fbmCustomerService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "客户管理-通过id查询")
    @ApiOperation(value = "客户管理-通过id查询", notes = "客户管理-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        FbmCustomer fbmCustomer = fbmCustomerService.getById(id);
        return Result.OK(fbmCustomer);
    }
    /**
     * 导出数据
     *
     * @return
     */

    @AutoLog(value = "客户管理-导出数据")
    @ApiOperation(value = "客户管理-导出数据", notes = "客户管理-导出数据")
    @GetMapping(value = "/exportXls")
    public ModelAndView exportXls() {
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "客户信息数据导出报表");
        mv.addObject(NormalExcelConstants.CLASS, FbmCustomerVo.class);
        ExportParams exportParams = new ExportParams();
        exportParams.setType(ExcelType.XSSF);
        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
        List<FbmCustomer> list = fbmCustomerService.list();
        ArrayList<FbmCustomerVo> fbmCustomerVos = new ArrayList<>();
        for (FbmCustomer fbmCustomer : list) {
            FbmCustomerVo fbmCustomerVo = BeanUtil.copyProperties(fbmCustomer, FbmCustomerVo.class);
            FbmCustomerCount byId = fbmCustomerCountService.getById(fbmCustomer.getId());
            if (byId == null) {
                fbmCustomerVo.setNumberDetectedWorks("0");
                fbmCustomerVo.setNumberTortData("0");
                fbmCustomerVo.setNumberSite("0");
                continue;
            }
            fbmCustomerVo.setNumberDetectedWorks(byId.getWorksCount());
            fbmCustomerVo.setNumberTortData(byId.getTortCount());
            fbmCustomerVo.setNumberSite(byId.getSiteCount());
            fbmCustomerVos.add(fbmCustomerVo);
        }
        mv.addObject(NormalExcelConstants.DATA_LIST, fbmCustomerVos);
        return mv;
    }
    /**
     * 数据总统计
     *
     * @return
     */
    @AutoLog(value = "客户管理-数据总统计")
    @ApiOperation(value = "客户管理-数据总统计", notes = "客户管理-数据总统计")
    @GetMapping(value = "/count")
    public Result<Map<String,String>> getCount() {
        List<FbmCustomerCount> list = fbmCustomerCountService.list();
        long count = fbmCustomerService.count();
        Long siteCount = 0L;
        Long tortCount = 0L;
        Long worksCount = 0L;
        for (FbmCustomerCount fbmCustomerCount : list) {
             tortCount += Long.parseLong(fbmCustomerCount.getTortCount());
             worksCount += Long.parseLong(fbmCustomerCount.getWorksCount());
             siteCount += Long.parseLong(fbmCustomerCount.getSiteCount());
        }
        HashMap<String, String> fbmCustomerCount = new HashMap<>();
        fbmCustomerCount.put("siteCount", String.valueOf(siteCount));
        fbmCustomerCount.put("tortCount", String.valueOf(tortCount));
        fbmCustomerCount.put("worksCount", String.valueOf(worksCount));
        fbmCustomerCount.put("customerCount", String.valueOf(count));
        return Result.OK(fbmCustomerCount);
    }



}
