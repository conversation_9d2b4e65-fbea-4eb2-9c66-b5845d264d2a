package com.firstbrave.controller.job;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.PageUtil;
import org.jeecg.common.util.filter.SsrfFileTypeFilter;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.entity.param.MiguJobEditParam;
import org.jeecg.entity.param.MiguJobParam;
import org.jeecg.entity.po.MiguJob;
import org.jeecg.modules.platform.service.IMiguJobService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.*;

import static org.jeecg.common.util.CommonUtils.uploadLocalFile;

/**
 * @Description: 任务管理
 * @Author: fbi
 * @Date: 2025-08-25
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "任务管理")
@RestController
@RequestMapping("/Job")
public class MiguJobController extends JeecgController<MiguJob, IMiguJobService> {
    @Resource
    private IMiguJobService miguJobService;
    @Value(value = "${jeecg.path.upload}")
    private String uploadPath;

    /**
     * 分页列表查询
     *
     * @param miguJob
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "分页列表查询")
    @ApiOperation(value = "分页列表查询", notes = "分页列表查询")
    @PostMapping(value = "/list")
    public Result<IPage<MiguJobParam>> queryPageList(@RequestBody MiguJob miguJob,
                                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                     HttpServletRequest req) {
        QueryWrapper<MiguJob> queryWrapper = QueryGenerator.initQueryWrapper(miguJob, req.getParameterMap());
        Page<MiguJob> page = new Page<MiguJob>(pageNo, pageSize);
        IPage<MiguJob> pageList = miguJobService.page(page, queryWrapper);
        Page<MiguJobParam> generate = PageUtil.generate(pageList, MiguJobParam.class);
        List<MiguJob> records = pageList.getRecords();
        ArrayList<MiguJobParam> miguJobParams = new ArrayList<>();
        for (MiguJob miguJob1 : records) {
            MiguJobParam miguJobParam = miguJobToMiguJobParam(miguJob1);
            miguJobParams.add(miguJobParam);
        }
        generate.setRecords(miguJobParams);
        return Result.OK(generate);
    }

    /**
     * 添加
     *
     * @param miguJobParam
     * @return
     */
    @AutoLog(value = "添加")
    @ApiOperation(value = "添加", notes = "添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody MiguJobParam miguJobParam) {
        MiguJob miguJob = miguJobParamToMiguJob(miguJobParam);
        miguJobService.save(miguJob);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param miguJobeditParam
     * @return
     */
    @AutoLog(value = "编辑")
    @ApiOperation(value = "编辑", notes = "编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody MiguJobEditParam miguJobeditParam) {
        MiguJobParam miguJobParam = BeanUtil.copyProperties(miguJobeditParam, MiguJobParam.class);
        MiguJob miguJob = miguJobParamToMiguJob(miguJobParam);
        miguJobService.updateById(miguJob);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        miguJobService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "批量删除")
    @ApiOperation(value = "批量删除", notes = "批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.miguJobService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "通过id查询")
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        MiguJob miguJob = miguJobService.getById(id);
        MiguJobParam miguJobParam = miguJobToMiguJobParam(miguJob);
        return Result.OK(miguJobParam);
    }

    public MiguJobParam miguJobToMiguJobParam(MiguJob miguJob) {
        if (miguJob == null) {
            return new MiguJobParam();
        }
        MiguJobParam miguJobParam = new MiguJobParam();
        BeanUtil.copyProperties(miguJob, miguJobParam);
        if (miguJob.getAttachmentPath() != null) {
            miguJobParam.setAttachmentPath(JSON.parseArray(miguJob.getAttachmentPath(), String.class));
        }
        if (miguJob.getDemand() != null) {
            miguJobParam.setDemandList(JSON.parseArray(miguJob.getDemand(), String.class));
        }
        if (miguJob.getSite() != null) {
            miguJobParam.setSiteList(JSON.parseArray(miguJob.getSite(), String.class));
        }
        if (miguJob.getLiveTime() != null) {
            miguJobParam.setLiveTimeList(JSON.parseArray(miguJob.getLiveTime(), String.class));
        }
        if (miguJob.getWhiteInfo() != null) {
            miguJobParam.setWhiteInfoList(JSON.parseArray(miguJob.getWhiteInfo(), String.class));
        }
        return miguJobParam;

    }

    public MiguJob miguJobParamToMiguJob(MiguJobParam miguJobParam) {
        if (miguJobParam == null) {
            return new MiguJob();
        }
        MiguJob miguJob = new MiguJob();
        BeanUtil.copyProperties(miguJobParam, miguJob);
        if (CollUtil.isNotEmpty(miguJobParam.getDemandList())) {
            miguJob.setDemand(JSON.toJSONString(miguJobParam.getDemandList()));
        }
        if (CollUtil.isNotEmpty(miguJobParam.getAttachmentPath())) {
            ArrayList<String> filePathList = new ArrayList<>();
            for (String filePath : miguJobParam.getAttachmentPath()) {
                //绝对路径
                String filePath1 = uploadPath + "/" + filePath;
                filePathList.add(filePath1);
            }
            miguJob.setAttachmentPath(JSON.toJSONString(filePathList));
        }
        if (CollUtil.isNotEmpty(miguJobParam.getSiteList())) {
            miguJob.setSite(JSON.toJSONString(miguJobParam.getSiteList()));
        }
        if (CollUtil.isNotEmpty(miguJobParam.getLiveTimeList())) {
            miguJob.setLiveTime(JSON.toJSONString(miguJobParam.getLiveTimeList()));
        }
        if (CollUtil.isNotEmpty(miguJobParam.getWhiteInfoList())) {
            miguJob.setWhiteInfo(JSON.toJSONString(miguJobParam.getWhiteInfoList()));
        }
        return miguJob;
    }

    /**
     * 单文件上传
     *
     * @param file    文件
     * @param bizPath 业务路径（可选）
     * @return
     */
    @AutoLog(value = "文件上传")
    @ApiOperation(value = "文件上传", notes = "文件上传")
    @PostMapping("/file/upload")
    public Result<Map<String, Object>> uploadFile(@RequestParam("file") MultipartFile file,
                                                  @RequestParam(value = "bizPath", required = false) String bizPath) {
        Result<Map<String, Object>> result = new Result<>();

        try {
            // 安全检查
            if (file.isEmpty()) {
                return Result.error("上传文件不能为空");
            }

            // 过滤上传文件类型
            SsrfFileTypeFilter.checkUploadFileType(file);

            // 获取原始文件名
            String originalFilename = file.getOriginalFilename();
            String fileName = CommonUtils.getFileName(originalFilename);

            // 生成唯一文件名
            String fileExtension = "";
            if (fileName.contains(".")) {
                fileExtension = fileName.substring(fileName.lastIndexOf("."));
                fileName = fileName.substring(0, fileName.lastIndexOf("."));
            }
            String uniqueFileName = fileName + "_" + System.currentTimeMillis() + fileExtension;

            // 设置默认业务路径
            if (oConvertUtils.isEmpty(bizPath)) {
                bizPath = "job";
            }

            // 安全检查业务路径
            if (bizPath.contains("../") || bizPath.contains("..\\")) {
                throw new JeecgBootException("上传目录bizPath，格式非法！");
            }

            String filePath;
            String fileUrl;
            // 本地文件上传
            filePath = uploadLocalFile(file, bizPath, uniqueFileName, uploadPath);
            fileUrl = "/file/download?filePath=" + filePath;
            // 构建返回结果
            Map<String, Object> fileInfo = new HashMap<>();
            fileInfo.put("originalName", originalFilename);
            fileInfo.put("fileName", uniqueFileName);
            fileInfo.put("filePath", filePath);
            fileInfo.put("fileUrl", fileUrl);
            fileInfo.put("fileSize", file.getSize());
            fileInfo.put("fileType", file.getContentType());
            fileInfo.put("uploadTime", new Date());
            result.setResult(fileInfo);
            result.setSuccess(true);
            result.setMessage("文件上传成功");

        } catch (Exception e) {
            log.error("文件上传失败", e);
            result.setSuccess(false);
            result.setMessage("文件上传失败：" + e.getMessage());
        }

        return result;
    }


    /**
     * 文件下载
     *
     * @param filePath 文件路径
     * @param response 响应
     */
    @AutoLog(value = "文件下载")
    @ApiOperation(value = "文件下载", notes = "文件下载")
    @GetMapping("/file/download")
    public void downloadFile(@RequestParam("filePath") String filePath,
                             HttpServletResponse response) {
        CommonUtils.downloadFile(response, filePath, uploadPath);
    }

    /**
     * 删除文件
     *
     * @param filePath 文件路径
     * @return
     */
    @DeleteMapping("/file/delete")
    public Result<?> deleteFile(@RequestParam("filePath") String filePath) {
        try {
            if (oConvertUtils.isEmpty(filePath)) {
                return Result.error("文件路径不能为空");
            }

            // 安全检查文件路径
            if (filePath.contains("../") || filePath.contains("..\\")) {
                throw new JeecgBootException("文件路径格式非法！");
            }

            // 本地文件删除
            File file = new File(uploadPath + File.separator + filePath);
            if (file.exists()) {
                boolean deleted = file.delete();
                if (deleted) {
                    return Result.OK("文件删除成功");
                } else {
                    return Result.error("文件删除失败");
                }
            } else {
                return Result.error("文件不存在");
            }
        } catch (Exception e) {
            log.error("文件删除失败", e);
            return Result.error("文件删除失败：" + e.getMessage());
        }
    }


}
